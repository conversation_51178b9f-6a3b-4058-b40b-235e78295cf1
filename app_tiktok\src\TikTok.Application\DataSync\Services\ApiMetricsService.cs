using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace TikTok.DataSync.Services
{
    /// <summary>
    /// Service để theo dõi và đo lường metrics API
    /// </summary>
    public interface IApiMetricsService
    {
        /// <summary>
        /// Bắt đầu theo dõi metrics cho service
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void StartTracking(string serviceName);

        /// <summary>
        /// Ghi nhận một lần gọi API
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void RecordApiCall(string serviceName);

        /// <summary>
        /// Kết thúc theo dõi và lấy metrics
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        /// <returns>ApiMetrics</returns>
        ApiMetrics GetMetrics(string serviceName);

        /// <summary>
        /// Reset metrics cho service
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void ResetMetrics(string serviceName);
    }

    /// <summary>
    /// Implementation của ApiMetricsService
    /// </summary>
    public class ApiMetricsService : IApiMetricsService
    {
        private readonly ILogger<ApiMetricsService> _logger;
        private readonly ConcurrentDictionary<string, ServiceMetrics> _serviceMetrics;

        public ApiMetricsService(ILogger<ApiMetricsService> logger)
        {
            _logger = logger;
            _serviceMetrics = new ConcurrentDictionary<string, ServiceMetrics>();
        }

        public void StartTracking(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            var now = DateTime.UtcNow;
            _serviceMetrics.AddOrUpdate(serviceName, 
                new ServiceMetrics 
                { 
                    ServiceName = serviceName, 
                    StartTime = now,
                    LastResetDate = now.Date
                },
                (key, existing) => 
                {
                    // Nếu đã qua ngày mới, reset counter
                    if (existing.LastResetDate < now.Date)
                    {
                        existing.TotalApiCallsToday = 0;
                        existing.LastResetDate = now.Date;
                        existing.ApiCallTimes.Clear();
                    }
                    existing.StartTime = now;
                    return existing;
                });

            _logger.LogDebug("Bắt đầu theo dõi metrics cho service: {ServiceName}", serviceName);
        }

        public void RecordApiCall(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            var now = DateTime.UtcNow;
            _serviceMetrics.AddOrUpdate(serviceName,
                new ServiceMetrics 
                { 
                    ServiceName = serviceName,
                    StartTime = now,
                    TotalApiCallsToday = 1,
                    LastResetDate = now.Date,
                    ApiCallTimes = new List<DateTime> { now }
                },
                (key, existing) => 
                {
                    // Nếu đã qua ngày mới, reset counter
                    if (existing.LastResetDate < now.Date)
                    {
                        existing.TotalApiCallsToday = 0;
                        existing.LastResetDate = now.Date;
                        existing.ApiCallTimes.Clear();
                    }

                    existing.TotalApiCallsToday++;
                    existing.ApiCallTimes.Add(now);
                    
                    // Chỉ giữ lại các lần gọi trong 60 giây gần nhất để tính rate
                    var cutoffTime = now.AddSeconds(-60);
                    existing.ApiCallTimes = existing.ApiCallTimes
                        .Where(t => t >= cutoffTime)
                        .ToList();
                    
                    return existing;
                });

            _logger.LogDebug("Ghi nhận API call cho service: {ServiceName}", serviceName);
        }

        public ApiMetrics GetMetrics(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
            {
                return new ApiMetrics { ServiceName = serviceName };
            }

            if (!_serviceMetrics.TryGetValue(serviceName, out var metrics))
            {
                return new ApiMetrics { ServiceName = serviceName };
            }

            var now = DateTime.UtcNow;
            
            // Tính rate gọi API theo giây (dựa trên 60 giây gần nhất)
            var cutoffTime = now.AddSeconds(-60);
            var recentCalls = metrics.ApiCallTimes.Where(t => t >= cutoffTime).Count();
            var timeSpan = Math.Max(1, (now - (metrics.ApiCallTimes.FirstOrDefault(t => t >= cutoffTime) ?? now)).TotalSeconds);
            var apiCallsPerSecond = recentCalls / timeSpan;

            return new ApiMetrics
            {
                ServiceName = serviceName,
                ApiCallsPerSecond = Math.Round(apiCallsPerSecond, 2),
                TotalApiCallsToday = metrics.TotalApiCallsToday,
                StartTime = metrics.StartTime,
                EndTime = now
            };
        }

        public void ResetMetrics(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            _serviceMetrics.TryRemove(serviceName, out _);
            _logger.LogDebug("Reset metrics cho service: {ServiceName}", serviceName);
        }

        /// <summary>
        /// Class nội bộ để lưu trữ metrics của service
        /// </summary>
        private class ServiceMetrics
        {
            public string ServiceName { get; set; } = string.Empty;
            public DateTime StartTime { get; set; }
            public int TotalApiCallsToday { get; set; }
            public DateTime LastResetDate { get; set; }
            public List<DateTime> ApiCallTimes { get; set; } = new List<DateTime>();
        }
    }
}
