using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Volo.Abp;

namespace TikTok.DataSync.Services
{
    /// <summary>
    /// Service để theo dõi và đo lường metrics API
    /// </summary>
    public interface IApiMetricsService
    {
        /// <summary>
        /// Bắt đầu theo dõi metrics cho service
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void StartTracking(string serviceName);

        /// <summary>
        /// Ghi nhận một lần gọi API
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        /// <returns>True nếu có thể gọi API, False nếu đã vượt giới hạn</returns>
        bool RecordApiCall(string serviceName);

        /// <summary>
        /// Kết thúc theo dõi và lấy metrics
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        /// <returns>ApiMetrics</returns>
        ApiMetrics GetMetrics(string serviceName);

        /// <summary>
        /// Reset metrics cho service
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void ResetMetrics(string serviceName);

        /// <summary>
        /// Kiểm tra xem service có thể gọi API hay không (dựa trên giới hạn development)
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        /// <returns>True nếu có thể gọi API, False nếu đã vượt giới hạn</returns>
        bool CanMakeApiCall(string serviceName);

        /// <summary>
        /// Throw exception nếu vượt giới hạn API calls trong development
        /// </summary>
        /// <param name="serviceName">Tên service</param>
        void ThrowIfExceedsLimit(string serviceName);
    }

    /// <summary>
    /// Implementation của ApiMetricsService
    /// </summary>
    public class ApiMetricsService : IApiMetricsService
    {
        private readonly ILogger<ApiMetricsService> _logger;
        private readonly IHostEnvironment _hostEnvironment;
        private readonly ConcurrentDictionary<string, ServiceMetrics> _serviceMetrics;
        private const int DEVELOPMENT_API_LIMIT = 10;

        public ApiMetricsService(ILogger<ApiMetricsService> logger, IHostEnvironment hostEnvironment)
        {
            _logger = logger;
            _hostEnvironment = hostEnvironment;
            _serviceMetrics = new ConcurrentDictionary<string, ServiceMetrics>();
        }

        public void StartTracking(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            var now = DateTime.UtcNow;
            _serviceMetrics.AddOrUpdate(serviceName, 
                new ServiceMetrics 
                { 
                    ServiceName = serviceName, 
                    StartTime = now,
                    LastResetDate = now.Date
                },
                (key, existing) => 
                {
                    // Nếu đã qua ngày mới, reset counter
                    if (existing.LastResetDate < now.Date)
                    {
                        existing.TotalApiCallsToday = 0;
                        existing.LastResetDate = now.Date;
                        existing.ApiCallTimes.Clear();
                    }
                    existing.StartTime = now;
                    return existing;
                });

            _logger.LogDebug("Bắt đầu theo dõi metrics cho service: {ServiceName}", serviceName);
        }

        public bool RecordApiCall(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return false;

            var now = DateTime.UtcNow;
            bool canMakeApiCall = true;

            _serviceMetrics.AddOrUpdate(serviceName,
                new ServiceMetrics
                {
                    ServiceName = serviceName,
                    StartTime = now,
                    TotalApiCallsToday = 1,
                    LastResetDate = now.Date,
                    ApiCallTimes = new List<DateTime> { now }
                },
                (key, existing) =>
                {
                    // Nếu đã qua ngày mới, reset counter
                    if (existing.LastResetDate < now.Date)
                    {
                        existing.TotalApiCallsToday = 0;
                        existing.LastResetDate = now.Date;
                        existing.ApiCallTimes.Clear();
                    }

                    // Kiểm tra giới hạn trong môi trường development
                    if (_hostEnvironment.IsDevelopment())
                    {
                        if (existing.TotalApiCallsToday >= DEVELOPMENT_API_LIMIT)
                        {
                            canMakeApiCall = false;
                            _logger.LogWarning("Service {ServiceName} đã vượt giới hạn {Limit} API calls trong môi trường Development. Hiện tại: {Current} calls",
                                serviceName, DEVELOPMENT_API_LIMIT, existing.TotalApiCallsToday);
                            return existing; // Không tăng counter nếu vượt giới hạn
                        }
                    }

                    existing.TotalApiCallsToday++;
                    existing.ApiCallTimes.Add(now);

                    // Chỉ giữ lại các lần gọi trong 60 giây gần nhất để tính rate
                    var cutoffTime = now.AddSeconds(-60);
                    existing.ApiCallTimes = existing.ApiCallTimes
                        .Where(t => t >= cutoffTime)
                        .ToList();

                    return existing;
                });

            if (canMakeApiCall)
            {
                _logger.LogDebug("Ghi nhận API call cho service: {ServiceName}", serviceName);
            }

            return canMakeApiCall;
        }

        public ApiMetrics GetMetrics(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
            {
                return new ApiMetrics { ServiceName = serviceName };
            }

            if (!_serviceMetrics.TryGetValue(serviceName, out var metrics))
            {
                return new ApiMetrics { ServiceName = serviceName };
            }

            var now = DateTime.UtcNow;
            
            // Tính rate gọi API theo giây (dựa trên 60 giây gần nhất)
            var cutoffTime = now.AddSeconds(-60);
            var recentCalls = metrics.ApiCallTimes.Where(t => t >= cutoffTime).Count();
            var timeSpan = Math.Max(1, (now - (metrics.ApiCallTimes.FirstOrDefault(t => t >= cutoffTime))).TotalSeconds);
            var apiCallsPerSecond = recentCalls / timeSpan;

            return new ApiMetrics
            {
                ServiceName = serviceName,
                ApiCallsPerSecond = Math.Round(apiCallsPerSecond, 2),
                TotalApiCallsToday = metrics.TotalApiCallsToday,
                StartTime = metrics.StartTime,
                EndTime = now
            };
        }

        public void ResetMetrics(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return;

            _serviceMetrics.TryRemove(serviceName, out _);
            _logger.LogDebug("Reset metrics cho service: {ServiceName}", serviceName);
        }

        public bool CanMakeApiCall(string serviceName)
        {
            if (string.IsNullOrEmpty(serviceName))
                return false;

            // Nếu không phải development, luôn cho phép
            if (!_hostEnvironment.IsDevelopment())
                return true;

            if (!_serviceMetrics.TryGetValue(serviceName, out var metrics))
                return true; // Chưa có metrics, cho phép gọi

            var now = DateTime.UtcNow;

            // Nếu đã qua ngày mới, reset counter
            if (metrics.LastResetDate < now.Date)
                return true; // Ngày mới, cho phép gọi

            return metrics.TotalApiCallsToday < DEVELOPMENT_API_LIMIT;
        }

        public void ThrowIfExceedsLimit(string serviceName)
        {
            if (!CanMakeApiCall(serviceName))
            {
                var currentCount = _serviceMetrics.TryGetValue(serviceName, out var metrics) ? metrics.TotalApiCallsToday : 0;
                throw new BusinessException(
                    "API_LIMIT_EXCEEDED",
                    $"Service {serviceName} đã vượt giới hạn {DEVELOPMENT_API_LIMIT} API calls trong môi trường Development. Hiện tại: {currentCount} calls.");
            }
        }

        /// <summary>
        /// Class nội bộ để lưu trữ metrics của service
        /// </summary>
        private class ServiceMetrics
        {
            public string ServiceName { get; set; } = string.Empty;
            public DateTime StartTime { get; set; }
            public int TotalApiCallsToday { get; set; }
            public DateTime LastResetDate { get; set; }
            public List<DateTime> ApiCallTimes { get; set; } = new List<DateTime>();
        }
    }
}
