# API Metrics cho GMV Max Sync Services

## Tổng quan

Tính năng API Metrics đã được thêm vào các GMV Max Sync Services để theo dõi:
- **Rate gọi API theo giây** (số lượt gọi/giây)
- **Tổng số lượt gọi API cho mỗi service theo ngày**

## Các Services được tích hợp

1. **GmvMaxProductCreativeSyncService**
2. **GmvMaxProductCampaignSyncService** 
3. **GmvMaxLiveCampaignSyncService**

## Cấu trúc ApiMetrics

```csharp
public class ApiMetrics
{
    /// <summary>
    /// Tên service
    /// </summary>
    public string ServiceName { get; set; }

    /// <summary>
    /// Rate gọi API theo giây (số lượt gọi/giây)
    /// </summary>
    public double ApiCallsPerSecond { get; set; }

    /// <summary>
    /// Tổng số lượt gọi API cho service trong ngày
    /// </summary>
    public int TotalApiCallsToday { get; set; }

    /// <summary>
    /// Thời gian bắt đầu đo lường
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Thời gian kết thúc đo lường
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// Tổng thời gian thực hiện (giây)
    /// </summary>
    public double TotalDurationSeconds { get; }
}
```

## Cách sử dụng

### 1. Gọi Service và lấy Metrics

```csharp
// Gọi service sync
var result = await _productCreativeSyncService.SyncGmvMaxProductCreativeAsync(bcId);

// Kiểm tra metrics
if (result.IsSuccess && result.ApiMetrics != null)
{
    Console.WriteLine($"Service: {result.ApiMetrics.ServiceName}");
    Console.WriteLine($"Rate gọi API: {result.ApiMetrics.ApiCallsPerSecond} calls/second");
    Console.WriteLine($"Tổng API calls hôm nay: {result.ApiMetrics.TotalApiCallsToday}");
    Console.WriteLine($"Thời gian thực hiện: {result.ApiMetrics.TotalDurationSeconds} seconds");
}
```

### 2. So sánh Metrics giữa các Services

```csharp
var productCreativeResult = await _productCreativeSyncService.SyncGmvMaxProductCreativeAsync(bcId);
var productCampaignResult = await _productCampaignSyncService.SyncGmvMaxProductCampaignAsync(bcId);
var liveCampaignResult = await _liveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(bcId);

// So sánh rate gọi API
Console.WriteLine("=== SO SÁNH API METRICS ===");
Console.WriteLine($"Product Creative: {productCreativeResult.ApiMetrics?.ApiCallsPerSecond} calls/sec");
Console.WriteLine($"Product Campaign: {productCampaignResult.ApiMetrics?.ApiCallsPerSecond} calls/sec");
Console.WriteLine($"Live Campaign: {liveCampaignResult.ApiMetrics?.ApiCallsPerSecond} calls/sec");

// Tính tổng API calls
var totalApiCalls = (productCreativeResult.ApiMetrics?.TotalApiCallsToday ?? 0) +
                   (productCampaignResult.ApiMetrics?.TotalApiCallsToday ?? 0) +
                   (liveCampaignResult.ApiMetrics?.TotalApiCallsToday ?? 0);
Console.WriteLine($"Tổng API calls hôm nay: {totalApiCalls}");
```

## Cách hoạt động

### 1. Tracking tự động
- Khi gọi `SyncAllGmvMaxProductCreativeForAllBcsAsync()` hoặc `SyncGmvMaxProductCreativeAsync()`, service tự động bắt đầu tracking metrics
- Mỗi lần gọi API TikTok được ghi nhận với timestamp

### 2. Tính toán Rate
- Rate gọi API được tính dựa trên 60 giây gần nhất
- Công thức: `số lượt gọi trong 60s / thời gian thực tế`

### 3. Reset hàng ngày
- Counter `TotalApiCallsToday` tự động reset vào 00:00 UTC mỗi ngày
- Lịch sử gọi API cũ được xóa để tiết kiệm memory

## Lưu ý quan trọng

### 1. Thread-safe
- `ApiMetricsService` sử dụng `ConcurrentDictionary` để đảm bảo thread-safe
- Có thể gọi đồng thời từ nhiều thread

### 2. Memory Management
- Chỉ lưu trữ timestamp của 60 giây gần nhất
- Tự động cleanup dữ liệu cũ

### 3. Singleton Service
- `ApiMetricsService` được đăng ký như singleton
- Metrics được chia sẻ giữa tất cả instances của sync services

## Ví dụ Output

```
=== API METRICS ===
Service: GmvMaxProductCreativeSyncService
Rate gọi API: 2.5 calls/second
Tổng API calls hôm nay: 150
Thời gian thực hiện: 60.5 seconds
Từ: 2024-01-15 10:30:00 đến 2024-01-15 10:31:00

=== SO SÁNH API METRICS CÁC SERVICES ===
Product Creative: 2.5 calls/sec, 150 calls today
Product Campaign: 1.8 calls/sec, 89 calls today  
Live Campaign: 3.2 calls/sec, 203 calls today
Tổng API calls hôm nay từ tất cả services: 442
```

## Troubleshooting

### 1. Metrics null
- Kiểm tra `ApiMetricsService` đã được đăng ký trong DI container
- Đảm bảo service được inject đúng cách

### 2. Rate không chính xác
- Rate được tính dựa trên 60 giây gần nhất
- Nếu chỉ có vài API calls, rate có thể thấp

### 3. TotalApiCallsToday reset
- Counter tự động reset vào 00:00 UTC
- Đây là behavior mong muốn để theo dõi theo ngày
