﻿using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using TikTok.AdAccounts;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.Application.MessageProviders;
using TikTok.BackgroundJobs;
using TikTok.BackgroundJobs.Workers;
using TikTok.Customers;
using TikTok.DataSync;
using TikTok.DataSync.Services;
using TikTok.DateTimes;
using TikTok.Extenstions;
using TikTok.JobManagement;
using TikTok.ResourceProviders;
using TikTok.ResourceProviders.AdAccounts;
using TikTok.TiktokResourceProviders;
using TikTok.TikTokApiClients;
using TikTok.Users;
using Tsp.Zalo;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.BackgroundJobs.Hangfire;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace TikTok;

[DependsOn(
    typeof(TikTokDomainModule),
    typeof(AbpAccountApplicationModule),
    typeof(TikTokApplicationContractsModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule),
    typeof(AbpBackgroundJobsHangfireModule),
    typeof(AbpBackgroundWorkersModule),
    typeof(ZaloApplicationModule)
    )]
public class TikTokApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<TikTokApplicationModule>();
        });

        // Đăng ký background job
        context.Services.AddTransient<WorkerJobBackgroundJob>();

        // Đăng ký Message Provider Options
        context.Services.Configure<MessageProviderOptions>(
            context.Services.GetConfiguration().GetSection("MessageProviderOption"));

        // Đăng ký HttpClient cho message providers
        context.Services.AddHttpClient();

        // Đăng ký Message Providers
        context.Services.AddTransient<IMessageProvider, TelegramMessageProvider>();
        context.Services.AddTransient<IMessageProvider, SlackMessageProvider>();

        // Đăng ký Message Service
        context.Services.AddTransient<IMessageService, MessageService>();

        // Đăng ký Notification Service
        context.Services.AddTransient<INotificationService, NotificationService>();

        // Đăng ký TiktokResourceProviderFactory
        context.Services.AddTransient<IResourceProviderFactory, ResourceProviderFactory>();

        // Đăng ký TiktokResourceProvider
        context.Services.AddScoped<IResourcePermissionProvider, AdAccountResourceProvider>();
        //context.Services.AddScoped<IAdAccountResourceProvider, AdAccountResourceProvider>();

        // Đăng ký TikTokApiClientService singleton
        context.Services.AddSingleton<ITikTokApiClientService, TikTokApiClientService>();

        // Đăng ký ApiMetricsService singleton để theo dõi metrics API
        context.Services.AddSingleton<IApiMetricsService, ApiMetricsService>();

        // Tự động đăng ký tất cả monitor cache services từ assembly hiện tại
        context.Services.AddAutoMonitorCacheServices(typeof(TikTokApplicationModule));
    }

    public override async Task OnApplicationInitializationAsync(
        ApplicationInitializationContext context)
    {
        await context.AddBackgroundWorkerAsync<JobHealthCheckWorker>();
        await context.AddBackgroundWorkerAsync<JobSystemStartupService>();
        await context.AddBackgroundWorkerAsync<JobCleanupWorker>();
        // TODO: Block
        await context.AddBackgroundWorkerAsync<SyncRawToFactBusinessCenterWorker>();
    }
}
