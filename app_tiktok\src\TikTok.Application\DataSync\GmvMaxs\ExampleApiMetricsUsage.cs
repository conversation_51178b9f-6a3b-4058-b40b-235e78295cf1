using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using TikTok.DataSync.Services;

namespace TikTok.DataSync.GmvMaxs
{
    /// <summary>
    /// V<PERSON> dụ về cách sử dụng API Metrics trong các sync services
    /// </summary>
    public class ExampleApiMetricsUsage
    {
        private readonly ILogger<ExampleApiMetricsUsage> _logger;
        private readonly IGmvMaxProductCreativeSyncService _productCreativeSyncService;
        private readonly IGmvMaxProductCampaignSyncService _productCampaignSyncService;
        private readonly IGmvMaxLiveCampaignSyncService _liveCampaignSyncService;

        public ExampleApiMetricsUsage(
            ILogger<ExampleApiMetricsUsage> logger,
            IGmvMaxProductCreativeSyncService productCreativeSyncService,
            IGmvMaxProductCampaignSyncService productCampaignSyncService,
            IGmvMaxLiveCampaignSyncService liveCampaignSyncService)
        {
            _logger = logger;
            _productCreativeSyncService = productCreativeSyncService;
            _productCampaignSyncService = productCampaignSyncService;
            _liveCampaignSyncService = liveCampaignSyncService;
        }

        /// <summary>
        /// Ví dụ sử dụng API metrics cho Product Creative Sync
        /// </summary>
        public async Task ExampleProductCreativeSyncWithMetricsAsync(string bcId)
        {
            try
            {
                _logger.LogInformation("Bắt đầu đồng bộ Product Creative với metrics tracking...");

                // Gọi service sync - metrics sẽ được tự động theo dõi
                var result = await _productCreativeSyncService.SyncGmvMaxProductCreativeAsync(bcId);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Đồng bộ thành công!");
                    _logger.LogInformation("Tổng bản ghi: {TotalSynced}", result.TotalSynced);
                    _logger.LogInformation("Bản ghi mới: {NewRecords}", result.NewRecords);
                    _logger.LogInformation("Bản ghi cập nhật: {UpdatedRecords}", result.UpdatedRecords);

                    // Hiển thị API metrics
                    if (result.ApiMetrics != null)
                    {
                        _logger.LogInformation("=== API METRICS ===");
                        _logger.LogInformation("Service: {ServiceName}", result.ApiMetrics.ServiceName);
                        _logger.LogInformation("Rate gọi API: {ApiCallsPerSecond} calls/second", result.ApiMetrics.ApiCallsPerSecond);
                        _logger.LogInformation("Tổng API calls hôm nay: {TotalApiCallsToday}", result.ApiMetrics.TotalApiCallsToday);
                        _logger.LogInformation("Thời gian thực hiện: {Duration} seconds", result.ApiMetrics.TotalDurationSeconds);
                        _logger.LogInformation("Từ: {StartTime} đến {EndTime}", 
                            result.ApiMetrics.StartTime.ToString("yyyy-MM-dd HH:mm:ss"), 
                            result.ApiMetrics.EndTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                }
                else
                {
                    _logger.LogError("Đồng bộ thất bại: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện đồng bộ Product Creative");
            }
        }

        /// <summary>
        /// Ví dụ sử dụng API metrics cho Product Campaign Sync
        /// </summary>
        public async Task ExampleProductCampaignSyncWithMetricsAsync(string bcId)
        {
            try
            {
                _logger.LogInformation("Bắt đầu đồng bộ Product Campaign với metrics tracking...");

                var result = await _productCampaignSyncService.SyncGmvMaxProductCampaignAsync(bcId);

                if (result.IsSuccess && result.ApiMetrics != null)
                {
                    _logger.LogInformation("=== PRODUCT CAMPAIGN API METRICS ===");
                    _logger.LogInformation("Service: {ServiceName}", result.ApiMetrics.ServiceName);
                    _logger.LogInformation("Rate: {Rate} calls/sec, Total today: {Total}", 
                        result.ApiMetrics.ApiCallsPerSecond, 
                        result.ApiMetrics.TotalApiCallsToday);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện đồng bộ Product Campaign");
            }
        }

        /// <summary>
        /// Ví dụ sử dụng API metrics cho Live Campaign Sync
        /// </summary>
        public async Task ExampleLiveCampaignSyncWithMetricsAsync(string bcId)
        {
            try
            {
                _logger.LogInformation("Bắt đầu đồng bộ Live Campaign với metrics tracking...");

                var result = await _liveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(bcId);

                if (result.IsSuccess && result.ApiMetrics != null)
                {
                    _logger.LogInformation("=== LIVE CAMPAIGN API METRICS ===");
                    _logger.LogInformation("Service: {ServiceName}", result.ApiMetrics.ServiceName);
                    _logger.LogInformation("Rate: {Rate} calls/sec, Total today: {Total}", 
                        result.ApiMetrics.ApiCallsPerSecond, 
                        result.ApiMetrics.TotalApiCallsToday);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi thực hiện đồng bộ Live Campaign");
            }
        }

        /// <summary>
        /// Ví dụ đồng bộ tất cả services và so sánh metrics
        /// </summary>
        public async Task ExampleCompareAllServicesMetricsAsync(string bcId)
        {
            try
            {
                _logger.LogInformation("Bắt đầu đồng bộ tất cả services và so sánh metrics...");

                // Đồng bộ tất cả services
                var productCreativeResult = await _productCreativeSyncService.SyncGmvMaxProductCreativeAsync(bcId);
                var productCampaignResult = await _productCampaignSyncService.SyncGmvMaxProductCampaignAsync(bcId);
                var liveCampaignResult = await _liveCampaignSyncService.SyncGmvMaxLiveCampaignAsync(bcId);

                // So sánh metrics
                _logger.LogInformation("=== SO SÁNH API METRICS CÁC SERVICES ===");
                
                if (productCreativeResult.ApiMetrics != null)
                {
                    _logger.LogInformation("Product Creative: {Rate} calls/sec, {Total} calls today", 
                        productCreativeResult.ApiMetrics.ApiCallsPerSecond,
                        productCreativeResult.ApiMetrics.TotalApiCallsToday);
                }

                if (productCampaignResult.ApiMetrics != null)
                {
                    _logger.LogInformation("Product Campaign: {Rate} calls/sec, {Total} calls today", 
                        productCampaignResult.ApiMetrics.ApiCallsPerSecond,
                        productCampaignResult.ApiMetrics.TotalApiCallsToday);
                }

                if (liveCampaignResult.ApiMetrics != null)
                {
                    _logger.LogInformation("Live Campaign: {Rate} calls/sec, {Total} calls today", 
                        liveCampaignResult.ApiMetrics.ApiCallsPerSecond,
                        liveCampaignResult.ApiMetrics.TotalApiCallsToday);
                }

                // Tính tổng API calls
                var totalApiCalls = (productCreativeResult.ApiMetrics?.TotalApiCallsToday ?? 0) +
                                   (productCampaignResult.ApiMetrics?.TotalApiCallsToday ?? 0) +
                                   (liveCampaignResult.ApiMetrics?.TotalApiCallsToday ?? 0);

                _logger.LogInformation("Tổng API calls hôm nay từ tất cả services: {TotalCalls}", totalApiCalls);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi so sánh metrics các services");
            }
        }
    }
}
